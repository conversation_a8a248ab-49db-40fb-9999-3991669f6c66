package com.bilibili.sycp.personal.mgk.portal.controller.view;

import com.bilibili.bjcom.util.common.Arguments;
import com.bilibili.sycp.personal.mgk.biz.common.util.GsonUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.framework.AopProxyUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.IOException;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 后门相关接口，仅当backdoor.enable为true时开启。
 *
 * <AUTHOR>
 * @since 2019年04月09日
 */
@Slf4j
@Controller
@RequestMapping("/backdoor")
@Tag(name = "BackdoorController-后门系统")
public class BackdoorController  {
    private static final Logger LOGGER = LoggerFactory.getLogger(BackdoorController.class);
    private static final Lock lock = new ReentrantLock();

    @Value("${personal.mgk.backdoor.enabled:true}")
    private boolean enable;

    @Autowired
    private ApplicationContext applicationContext;

    private final ObjectMapper om = new ObjectMapper();

    private static final Pattern BEAN_METHOD = Pattern.compile("([a-zA-Z0-9_$.]+)[.#]([a-zA-Z0-9_$]+)[（(](.*)[)）](\\d*)", Pattern.DOTALL);

    @RequestMapping
    @Operation(summary = "在线调试")
    public ResponseEntity<String> home(@RequestParam(value = "expression", required = false) String expression) {
        if (!enable) {
            return ResponseEntity.ok().body("Forbidden!");
        }

        if (lock.tryLock()) {
            try {
                String page = "<!DOCTYPE html>\n" +
                        "<html lang='en'>\n" +
                        "<head>\n" +
                        "    <meta charset='UTF-8'>\n" +
                        "    <title>在线调试</title>\n" +
                        "</head>\n" +
                        "<body>\n" +
                        "\n" +
                        "<form method='post' action=''>\n" +
                        "    <label>在线调试</label>\n" +
                        "    <br/>\n" +
                        "    <textarea rows='10' cols='120' name='expression' placeholder='" +
                        "用 Spring bean name 调用：userServiceImpl.findUser({id:123})\n" +
                        "用 IDEA 右键复制方法引用：com.ewing.UserServiceImpl#findUser({id:123})\n" +
                        "静态方法或new一个新对象调用：ewing.common.TimeUtils.getDaysOfMonth(2018,5)\n" +
                        "方法参数为去掉中括号的JSON数组，多个方法匹配则在表达式最后加数字指定调用哪个方法'>";

                // 写入参数
                page += expression == null ? "" : expression.trim();

                page += "</textarea>\n" +
                        "    <br/>\n" +
                        "    <input type='submit'  value='调用方法' style='width:863px'/>\n" +
                        "</form>\n" +
                        "<br/>\n" +
                        "<textarea rows='20' cols='120' placeholder='调用返回的结果'>";

                // 写入返回值
                try {
                    if (StringUtils.hasText(expression)) {
                        page += methodExecute(expression);
                    }
                } catch (Throwable throwable) {
                    Throwable cause = throwable;
                    while (throwable != null) {
                        cause = throwable;
                        throwable = cause.getCause();
                    }
                    page += "调用异常：" + cause.getMessage();
                }

                page += "</textarea>\n" +
                        "</body>\n" +
                        "</html>";

                return ResponseEntity.ok()
                        .contentType(MediaType.valueOf("text/html;charset=UTF-8"))
                        .body(page);
            } finally {
                lock.unlock();
            }
        } else {
            final String page = "<!DOCTYPE html>\n" +
                    "<html lang='en'>\n" +
                    "<head>\n" +
                    "    <meta charset='UTF-8'>\n" +
                    "    <title>在线调试</title>\n" +
                    "</head>\n" +
                    "<body>\n" +
                    "<p>后门系统正其他任务占用, 请稍后重试</p>\n" +
                    "</body>\n" +
                    "</html>";
            return ResponseEntity.ok()
                    .contentType(MediaType.valueOf("text/html;charset=UTF-8"))
                    .body(page);
        }
    }

    /***
     * 可执行项目中的任意方法，优先调用 Spring 代理的方法。
     *
     * @param expression 用 Spring bean name 调用：userServiceImpl.findUser({name:"元宝"})
     *                   用 IDEA 右键复制方法引用：com.ewing.UserServiceImpl#findUser({name:"元宝"})
     *                   静态方法或new一个新对象调用：ewing.common.TimeUtils.getDaysOfMonth(2018,5)
     *                   方法参数为去掉中括号的JSON数组，多个方法匹配则在表达式最后加数字指定调用哪个方法。
     */
    private String methodExecute(String expression) throws IOException {
        Assert.isTrue(StringUtils.hasText(expression), "表达式不能为空");
        Matcher matcher = BEAN_METHOD.matcher(expression);
        Assert.isTrue(matcher.matches(), "表达式格式不正确");

        String classOrBeanName = matcher.group(1);
        String methodName = matcher.group(2);
        final JsonNode arguments = om.readTree("[" + matcher.group(3) + "]");
        String indexGroup = matcher.group(4);
        boolean hasIndexGroup = StringUtils.hasText(indexGroup);
        int methodIndex = hasIndexGroup ? Integer.parseInt(indexGroup) : 0;

        // 根据名称获取Bean
        TargetInstance targetInstance = getTargetInstance(classOrBeanName);

        List<TargetInvoker> targetInvokers = getTargetInvokers(targetInstance, methodName, arguments);

        if (targetInvokers.size() > 1 && !hasIndexGroup) {
            StringBuilder resultBuilder = new StringBuilder("请在输入表达式之后加以下数字来选择调用的方法：");
            for (int i = 0; i < targetInvokers.size(); i++) {
                resultBuilder.append('\n').append(i).append(':').append(targetInvokers.get(i).method);
            }
            return resultBuilder.toString();
        }

        Arguments.of(targetInvokers).notEmpty("找不到满足参数的方法：" + methodName)
                .minSize(methodIndex + 1, "没有第 " + methodIndex + " 个可调用的方法");

        Object result = targetInvokers.get(methodIndex).invoke();
        try {
            return result instanceof String ? (String) result : GsonUtils.toJson(result);
        } catch (Exception e) {
            return String.valueOf(result);
        }
    }

    private static class TargetInstance {
        Object originTarget;
        Object advisedTarget;
        Class originTargetClass;
        Class advisedTargetClass;
    }

    private List<TargetInvoker> getTargetInvokers(TargetInstance targetInstance, String methodName, JsonNode arguments) {
        List<TargetInvoker> targetInvokers = new ArrayList<>();
        if (targetInstance == null) {
            return targetInvokers;
        }

        Object[] targets = {targetInstance.advisedTarget, targetInstance.originTarget};
        Class[] classes = {targetInstance.advisedTargetClass, targetInstance.originTargetClass};

        for (int i = 0; i < classes.length && targetInvokers.isEmpty(); i++) {
            Class clazz = classes[i];
            if (clazz == null) {
                continue;
            }
            for (Method method : clazz.getDeclaredMethods()) {
                if (!method.getName().equals(methodName)) continue;

                final Class<?>[] parameterTypes = method.getParameterTypes();
                if (!Objects.equals(parameterTypes.length, arguments.size())) continue;

                try {
                    final List<Object> args = new LinkedList<>();

                    int j = 0;
                    for (JsonNode argument : arguments) {
                        final Object o = om.treeToValue(argument, parameterTypes[j]);
                        args.add(o);
                        j++;
                    }

                    TargetInvoker targetInvoker = new TargetInvoker();
                    targetInvoker.arguments = args;
                    targetInvoker.method = method;
                    targetInvoker.target = targets[i];
                    targetInvokers.add(targetInvoker);
                } catch (Exception e) {
                    // 继续判断下一个方法是否满足条件
                }
            }
        }
        return targetInvokers;
    }

    private static class TargetInvoker {
        Method method;
        Object target;
        List<Object> arguments;

        Object invoke() {
            Method methodPresent = Objects.requireNonNull(this.method, "方法不能为空");
            Object invokeTarget = this.target;
            try {
                if (Modifier.isStatic(methodPresent.getModifiers())) {
                    invokeTarget = methodPresent.getDeclaringClass();
                } else if (invokeTarget == null) {
                    invokeTarget = methodPresent.getDeclaringClass().newInstance();
                }
                Object targetPresent = Objects.requireNonNull(invokeTarget, "实例不能为空");

                methodPresent.setAccessible(true);
                Object result = arguments == null || arguments.isEmpty() ?
                        methodPresent.invoke(targetPresent) :
                        methodPresent.invoke(targetPresent, arguments.toArray());

                return methodPresent.getReturnType().equals(Void.TYPE) ? "调用成功，该方法无返回值" : result;
            } catch (ReflectiveOperationException e) {
                LOGGER.error("调用方法失败", e);
                throw new RuntimeException(e);
            }
        }
    }

    private TargetInstance getTargetInstance(String classOrBeanName) {
        TargetInstance targetInstance = new TargetInstance();
        try {
            targetInstance.advisedTarget = applicationContext.getBean(classOrBeanName);
        } catch (Exception e) {
            LOGGER.info("Get spring bean name {} error: {}", classOrBeanName, e.getMessage());
        }

        if (targetInstance.advisedTarget == null) {
            try {
                targetInstance.advisedTarget = applicationContext.getBean(Class.forName(classOrBeanName));
            } catch (Exception e) {
                LOGGER.info("Get spring bean class {} error: {}", classOrBeanName, e.getMessage());
            }
        }

        if (targetInstance.advisedTarget == null) {
            try {
                targetInstance.originTargetClass = Class.forName(classOrBeanName);
            } catch (Exception e) {
                throw new RuntimeException("找不到类：" + classOrBeanName);
            }
        } else {
            targetInstance.advisedTargetClass = targetInstance.advisedTarget.getClass();
            targetInstance.originTarget = AopProxyUtils.getSingletonTarget(targetInstance.advisedTarget);
            targetInstance.originTargetClass = AopProxyUtils.ultimateTargetClass(targetInstance.advisedTarget);
        }
        return targetInstance;
    }

    @SneakyThrows
    private void mockLongUrl(Integer n) {
        final long version = System.currentTimeMillis();
        log.info("mockLongUrl version {} started", version);
        TimeUnit.SECONDS.sleep(n);
        log.info("mockLongUrl version {} done", version);
    }

    /**
     * 调试日志配置的方法
     */
    public String debugLogConfig() {
        StringBuilder result = new StringBuilder();

        // 测试不同级别的日志
        result.append("=== 日志级别测试 ===\n");
        log.trace("TRACE level log test");
        result.append("TRACE log executed\n");

        log.debug("DEBUG level log test");
        result.append("DEBUG log executed\n");

        log.info("INFO level log test");
        result.append("INFO log executed\n");

        log.warn("WARN level log test");
        result.append("WARN log executed\n");

        log.error("ERROR level log test");
        result.append("ERROR log executed\n");

        // 获取Logger信息
        result.append("\n=== Logger 信息 ===\n");
        result.append("Logger class: ").append(log.getClass().getName()).append("\n");

        // 尝试获取LoggerContext
        try {
            org.slf4j.LoggerFactory.getILoggerFactory();
            result.append("LoggerFactory class: ").append(org.slf4j.LoggerFactory.getILoggerFactory().getClass().getName()).append("\n");
        } catch (Exception e) {
            result.append("LoggerFactory error: ").append(e.getMessage()).append("\n");
        }

        // 检查特定包的Logger
        org.slf4j.Logger mgkLogger = org.slf4j.LoggerFactory.getLogger("com.bilibili.sycp.personal.mgk.biz");
        result.append("MGK package logger class: ").append(mgkLogger.getClass().getName()).append("\n");

        mgkLogger.info("MGK package INFO log test");
        result.append("MGK package INFO log executed\n");

        return result.toString();
    }
}
