package com.bilibili.sycp.personal.mgk.biz.tempus_job;

import com.bapis.ad.component.BizCodeEnum;
import com.bapis.ad.component.CommentComponentStatus;
import com.bilibili.sycp.personal.mgk.biz.api.comment_component.ICommentComponentService;
import com.bilibili.sycp.personal.mgk.biz.common.dto.Operator;
import com.bilibili.sycp.personal.mgk.biz.dao.ad.generated.DefaultSchema;
import com.bilibili.sycp.personal.mgk.biz.dao.ad.generated.tables.records.LauArchiveCommentConversionComponentRecord;
import com.bilibili.sycp.personal.mgk.biz.impl.account.AccountService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;
import tech.powerjob.worker.log.OmsLogger;

import javax.annotation.Resource;
import java.util.Optional;
import java.util.Set;

import static com.bilibili.sycp.personal.mgk.biz.impl.account.AccountService.PERSON_MGK_JEWELRY_MIDS_BLACKLIST_MAP;

/**
 * <AUTHOR>
 * @Description
 * @date 2024/10/8
 **/
@Slf4j
@Service
public class JewelryBlackMidComponentOfflineTempusJob implements BasicProcessor {

    private static final DefaultSchema adSchema = DefaultSchema.DEFAULT_SCHEMA;

    @Resource
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    private ICommentComponentService commentComponentService;
    @Resource
    private AccountService accountService;
    @Resource
    private DSLContext adDslContext;

    @Override
    public ProcessResult process(TaskContext context) {
        OmsLogger logger = context.getOmsLogger();

        String jobParams = Optional.ofNullable(context.getJobParams()).orElse("S");
        logger.info("Current job params:{}", jobParams);

        log.info("JewelryBlackMidComponentOfflineTempusJob start time:{}", System.currentTimeMillis());
        System.out.println("DEBUG: JewelryBlackMidComponentOfflineTempusJob start time: " + System.currentTimeMillis());
        Set<Object> keys = redisTemplate.opsForHash().keys(PERSON_MGK_JEWELRY_MIDS_BLACKLIST_MAP);
        for (Object midObj : keys) {
            Long mid = Long.parseLong(midObj.toString());
            if (!accountService.checkInJewelryBlackList(mid) && !accountService.checkIsRiskTags(mid)) {
                continue;
            }

            Integer accountId = accountService.checkPersonalFlyAccess(mid);
            var recordList = adDslContext
                    .fetch(adSchema.LAU_ARCHIVE_COMMENT_CONVERSION_COMPONENT,
                            adSchema.LAU_ARCHIVE_COMMENT_CONVERSION_COMPONENT.ACCOUNT_ID.in(accountId),
                            adSchema.LAU_ARCHIVE_COMMENT_CONVERSION_COMPONENT.BIZ_CODE.in(Lists.newArrayList(BizCodeEnum.BIZ_CODE_ENUM_PERSONAL_MGK_VALUE)),
                            adSchema.LAU_ARCHIVE_COMMENT_CONVERSION_COMPONENT.COMMENT_ID.greaterThan(0L),
                            adSchema.LAU_ARCHIVE_COMMENT_CONVERSION_COMPONENT.STATUS
                                    .eq(CommentComponentStatus.TOP.getNumber()));
            for (LauArchiveCommentConversionComponentRecord commentConversionComponentRecord : recordList) {
                Operator operator = Operator.SYSTEM;
                operator.setOperatorId(accountId);
                operator.setMid(mid);
                commentComponentService.deleteCommentConversionComponent(operator, Lists.newArrayList(commentConversionComponentRecord.getAid()));
            }
        }
        log.info("JewelryBlackMidComponentOfflineTempusJob end time:{}", System.currentTimeMillis());
        return jobParams.contains("F") ? new ProcessResult(false) : new ProcessResult(true, "success");
    }

}
